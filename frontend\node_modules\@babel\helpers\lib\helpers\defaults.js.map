{"version": 3, "names": ["_defaults", "obj", "defaults", "keys", "Object", "getOwnPropertyNames", "i", "length", "key", "value", "getOwnPropertyDescriptor", "configurable", "undefined", "defineProperty"], "sources": ["../../src/helpers/defaults.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nexport default function _defaults<T extends object, S extends object>(\n  obj: T,\n  defaults: S,\n): NonNullable<T & S> {\n  for (\n    var keys: string[] = Object.getOwnPropertyNames(defaults), i = 0;\n    i < keys.length;\n    i++\n  ) {\n    var key: string = keys[i],\n      value: PropertyDescriptor | undefined = Object.getOwnPropertyDescriptor(\n        defaults,\n        key,\n      );\n    if (value && value.configurable && obj[key as keyof T] === undefined) {\n      Object.defineProperty(obj, key, value);\n    }\n  }\n  return obj as NonNullable<T & S>;\n}\n"], "mappings": ";;;;;;AAEe,SAASA,SAASA,CAC/BC,GAAM,EACNC,QAAW,EACS;EACpB,KACE,IAAIC,IAAc,GAAGC,MAAM,CAACC,mBAAmB,CAACH,QAAQ,CAAC,EAAEI,CAAC,GAAG,CAAC,EAChEA,CAAC,GAAGH,IAAI,CAACI,MAAM,EACfD,CAAC,EAAE,EACH;IACA,IAAIE,GAAW,GAAGL,IAAI,CAACG,CAAC,CAAC;MACvBG,KAAqC,GAAGL,MAAM,CAACM,wBAAwB,CACrER,QAAQ,EACRM,GACF,CAAC;IACH,IAAIC,KAAK,IAAIA,KAAK,CAACE,YAAY,IAAIV,GAAG,CAACO,GAAG,CAAY,KAAKI,SAAS,EAAE;<PERSON>p<PERSON>,MAAM,CAACS,cAAc,CAACZ,GAAG,EAAEO,GAAG,EAAEC,KAAK,CAAC;IACxC;EACF;EACA,OAAOR,GAAG;AACZ", "ignoreList": []}