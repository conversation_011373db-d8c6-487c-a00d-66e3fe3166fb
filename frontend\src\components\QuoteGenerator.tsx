import React, { useState, useRef } from 'react';
import {
  Modal,
  Form,
  Input,
  DatePicker,
  Checkbox,
  Button,
  Space,
  Divider,
  Row,
  Col,
  Table,
  Typography,
  message
} from 'antd';
import { PrinterOutlined, DownloadOutlined } from '@ant-design/icons';
import { useReactToPrint } from 'react-to-print';
import dayjs from 'dayjs';
import type { CostAnalysis } from '../services/costAnalysisAPI';
import type { QuoteConfig, QuoteItem, QuoteData } from '../types/quote';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface QuoteGeneratorProps {
  visible: boolean;
  onCancel: () => void;
  selectedAnalyses: CostAnalysis[];
}

const QuoteGenerator: React.FC<QuoteGeneratorProps> = ({
  visible,
  onCancel,
  selectedAnalyses
}) => {
  const [form] = Form.useForm();
  const [previewVisible, setPreviewVisible] = useState(false);
  const [quoteData, setQuoteData] = useState<QuoteData | null>(null);
  const printRef = useRef<HTMLDivElement>(null);

  // 欄位顯示控制
  const [visibleColumns, setVisibleColumns] = useState(() => {
    const saved = localStorage.getItem('quote-visible-columns');
    return saved ? JSON.parse(saved) : {
      order: true,
      product_code: true,
      product_name: true,
      category: true,
      specifications: true,
      unit: true,
      quantity: true,
      unit_price: true,
      old_price: false,
      new_price: false,
      paper_material: true,
      moq: true,
      sticker_label: false,
      die_cutting: false,
      printing_fee: false,
      notes: true,
    };
  });

  // 保存欄位顯示設置到localStorage
  const updateVisibleColumns = (key: string, value: boolean) => {
    const newColumns = { ...visibleColumns, [key]: value };
    setVisibleColumns(newColumns);
    localStorage.setItem('quote-visible-columns', JSON.stringify(newColumns));
  };

  const handlePrint = useReactToPrint({
    contentRef: printRef,
    documentTitle: '報價單',
    onBeforePrint: () => {
      console.log('準備列印...');
      return Promise.resolve();
    },
    onAfterPrint: () => {
      console.log('列印完成');
      message.success('列印完成');
    },
    onPrintError: (errorLocation, error) => {
      console.error('列印錯誤:', errorLocation, error);
      message.error(`列印失敗: ${error.message || '請檢查瀏覽器設定'}`);
    },
    suppressErrors: false,
    pageStyle: `
      @page {
        size: A4;
        margin: 20mm;
      }
      @media print {
        body {
          -webkit-print-color-adjust: exact;
          color-adjust: exact;
        }
      }
    `,
  });



  // 將成本分析轉換為報價項目
  const convertToQuoteItems = (analyses: CostAnalysis[]): QuoteItem[] => {
    return analyses.map((analysis, index) => ({
      id: analysis.id.toString(),
      order: index + 1,
      product_code: analysis.supplier_code || '',
      product_name: `${analysis.product_name || '紙箱'} 纸箱`,
      category: 'A1',
      specifications: `${analysis.length || 0}*${analysis.width || 0}*${analysis.height || 0}mm`,
      unit: 'PCS',
      quantity: 1,
      unit_price: analysis.total_cost || 0,
      old_price: analysis.total_cost || 0,
      new_price: analysis.total_cost || 0,
      paper_material: analysis.paper_quality ? `${analysis.paper_quality}/185/125/185/145 - BC` : '235/185/125/185/145 - BC',
      moq: 1000,
      sticker_label: 1600,
      die_cutting: 1400000,
      printing_fee: 0,
      notes: analysis.notes || ''
    }));
  };

  const handleGenerateQuote = () => {
    form.validateFields().then(values => {
      const config: QuoteConfig = {
        company_name_zh: '新耀紙箱責任有限公司',
        company_name_vi: 'CÔNG TY TNHH NEW SHINE',
        address: 'Thửa đất số 390, tờ bản đồ số 27, Ấp 5, Xã Đức Hòa Đông, Huyện Đức Hòa, Tỉnh Long An',
        tax_number: '1102041293',
        phone: '0908929630 Mr.Liao - 0357878890 Ms.Trâm',
        customer_code: values.customer_code || '',
        customer_name: values.customer_name || '',
        customer_tax_number: values.customer_tax_number || '',
        customer_address: values.customer_address || '',
        quote_date: values.quote_date?.format('YYYY年MM月DD日') || dayjs().format('YYYY年MM月DD日'),
        include_printing_lamination: values.include_printing_lamination || false,
        vat_8_percent: values.vat_8_percent || false,
        vat_10_percent: values.vat_10_percent || false,
        minimum_order_amount: values.minimum_order_amount || false,
        payment_method_deposit: values.payment_method_deposit || false,
        payment_method_monthly: values.payment_method_monthly || false,
        payment_method_monthly_end: values.payment_method_monthly_end || false,
        payment_method_30_days: values.payment_method_30_days || false,
        delivery_location_warehouse: values.delivery_location_warehouse || false,
        delivery_location_pickup: values.delivery_location_pickup || false,
        delivery_location_custom: values.delivery_location_custom || '',
        quote_validity: values.quote_validity || false,
        delivery_time_7_10: values.delivery_time_7_10 || false,
        delivery_time_5_7: values.delivery_time_5_7 || false,
      };

      const items = convertToQuoteItems(selectedAnalyses);
      
      setQuoteData({ config, items });
      setPreviewVisible(true);
    });
  };

  const allColumns = [
    {
      title: '顺序\nSTT',
      dataIndex: 'order',
      key: 'order',
      width: 50,
      align: 'center' as const,
    },
    {
      title: '編號 / 料號\nMã hàng',
      dataIndex: 'product_code',
      key: 'product_code',
      width: 100,
    },
    {
      title: '名称\nTên Hàng',
      dataIndex: 'product_name',
      key: 'product_name',
      width: 120,
    },
    {
      title: '分類\nLoại',
      dataIndex: 'category',
      key: 'category',
      width: 60,
      align: 'center' as const,
    },
    {
      title: '规格\nQuy Cách (mm)',
      dataIndex: 'specifications',
      key: 'specifications',
      width: 120,
    },
    {
      title: '单位\nĐơn vị tính',
      dataIndex: 'unit',
      key: 'unit',
      width: 60,
      align: 'center' as const,
    },
    {
      title: '数量\nSố lượng',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 80,
      align: 'center' as const,
    },
    {
      title: '单价\nĐơn Gía',
      dataIndex: 'unit_price',
      key: 'unit_price',
      width: 100,
      align: 'right' as const,
      render: (value: number) => `${value.toLocaleString()} ₫`,
    },
    {
      title: '舊單價\nĐơn giá cũ',
      dataIndex: 'old_price',
      key: 'old_price',
      width: 100,
      align: 'right' as const,
      render: (value: number) => `${value.toLocaleString()} ₫`,
    },
    {
      title: '新單價\nĐơn giá mới',
      dataIndex: 'new_price',
      key: 'new_price',
      width: 100,
      align: 'right' as const,
      render: (value: number) => `${value.toLocaleString()} ₫`,
    },
    {
      title: '紙材\nChất liệu giấy',
      dataIndex: 'paper_material',
      key: 'paper_material',
      width: 150,
    },
    {
      title: 'MOQ / PCS',
      dataIndex: 'moq',
      key: 'moq',
      width: 80,
      align: 'center' as const,
      render: (value: number) => value.toLocaleString(),
    },
    {
      title: '贴纸 / 标签 (彩色的)\nTem dán màu, nhãn màu',
      dataIndex: 'sticker_label',
      key: 'sticker_label',
      width: 120,
      align: 'right' as const,
      render: (value: number) => value.toLocaleString(),
    },
    {
      title: '刀模\nKhuôn dao',
      dataIndex: 'die_cutting',
      key: 'die_cutting',
      width: 100,
      align: 'right' as const,
      render: (value: number) => `${value.toLocaleString()} ₫`,
    },
    {
      title: '印刷版费\nPhí in ấn',
      dataIndex: 'printing_fee',
      key: 'printing_fee',
      width: 100,
      align: 'right' as const,
      render: (value: number) => value > 0 ? `${value.toLocaleString()} ₫` : '',
    },
    {
      title: '備 註\nGhi chú',
      dataIndex: 'notes',
      key: 'notes',
      width: 150,
    },
  ];

  // 根據visibleColumns過濾要顯示的欄位
  const quoteColumns = allColumns.filter(column => visibleColumns[column.key as keyof typeof visibleColumns]);

  return (
    <>
      <Modal
        title="生成報價單"
        open={visible}
        onCancel={onCancel}
        width={800}
        footer={[
          <Button key="cancel" onClick={onCancel}>
            取消
          </Button>,
          <Button key="generate" type="primary" onClick={handleGenerateQuote}>
            生成報價單
          </Button>,
        ]}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            quote_date: dayjs(),
            include_printing_lamination: true,
            minimum_order_amount: true,
            quote_validity: true,
            delivery_time_7_10: true,
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="客戶代碼 / Mã KH"
                name="customer_code"
                rules={[{ required: true, message: '請輸入客戶代碼' }]}
              >
                <Input placeholder="例如: KIMTHANHPHAT" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="報價日期 / Ngày báo giá"
                name="quote_date"
                rules={[{ required: true, message: '請選擇報價日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="客戶名稱 / Khách hàng"
            name="customer_name"
            rules={[{ required: true, message: '請輸入客戶名稱' }]}
          >
            <Input placeholder="例如: CÔNG TY TNHH BỘT SƠN TĨNH ĐIỆN KIM THÀNH PHÁT" />
          </Form.Item>

          <Form.Item
            label="客戶稅號 / Mã số thuế"
            name="customer_tax_number"
          >
            <Input placeholder="例如: 1102089633" />
          </Form.Item>

          <Form.Item
            label="客戶地址 / Địa chỉ"
            name="customer_address"
          >
            <TextArea rows={2} placeholder="客戶完整地址" />
          </Form.Item>

          <Divider>表格欄位設置</Divider>

          <Row gutter={16}>
            <Col span={8}>
              <Checkbox
                checked={visibleColumns.order}
                onChange={(e) => updateVisibleColumns('order', e.target.checked)}
              >
                顺序 STT
              </Checkbox>
              <br />
              <Checkbox
                checked={visibleColumns.product_code}
                onChange={(e) => updateVisibleColumns('product_code', e.target.checked)}
              >
                編號/料號
              </Checkbox>
              <br />
              <Checkbox
                checked={visibleColumns.product_name}
                onChange={(e) => updateVisibleColumns('product_name', e.target.checked)}
              >
                名称
              </Checkbox>
              <br />
              <Checkbox
                checked={visibleColumns.category}
                onChange={(e) => updateVisibleColumns('category', e.target.checked)}
              >
                分類
              </Checkbox>
              <br />
              <Checkbox
                checked={visibleColumns.specifications}
                onChange={(e) => updateVisibleColumns('specifications', e.target.checked)}
              >
                规格
              </Checkbox>
            </Col>
            <Col span={8}>
              <Checkbox
                checked={visibleColumns.unit}
                onChange={(e) => updateVisibleColumns('unit', e.target.checked)}
              >
                单位
              </Checkbox>
              <br />
              <Checkbox
                checked={visibleColumns.quantity}
                onChange={(e) => updateVisibleColumns('quantity', e.target.checked)}
              >
                数量
              </Checkbox>
              <br />
              <Checkbox
                checked={visibleColumns.unit_price}
                onChange={(e) => updateVisibleColumns('unit_price', e.target.checked)}
              >
                单价
              </Checkbox>
              <br />
              <Checkbox
                checked={visibleColumns.old_price}
                onChange={(e) => updateVisibleColumns('old_price', e.target.checked)}
              >
                舊單價
              </Checkbox>
              <br />
              <Checkbox
                checked={visibleColumns.new_price}
                onChange={(e) => updateVisibleColumns('new_price', e.target.checked)}
              >
                新單價
              </Checkbox>
            </Col>
            <Col span={8}>
              <Checkbox
                checked={visibleColumns.paper_material}
                onChange={(e) => updateVisibleColumns('paper_material', e.target.checked)}
              >
                紙材
              </Checkbox>
              <br />
              <Checkbox
                checked={visibleColumns.moq}
                onChange={(e) => updateVisibleColumns('moq', e.target.checked)}
              >
                MOQ/PCS
              </Checkbox>
              <br />
              <Checkbox
                checked={visibleColumns.sticker_label}
                onChange={(e) => updateVisibleColumns('sticker_label', e.target.checked)}
              >
                贴纸/标签
              </Checkbox>
              <br />
              <Checkbox
                checked={visibleColumns.die_cutting}
                onChange={(e) => updateVisibleColumns('die_cutting', e.target.checked)}
              >
                刀模
              </Checkbox>
              <br />
              <Checkbox
                checked={visibleColumns.printing_fee}
                onChange={(e) => updateVisibleColumns('printing_fee', e.target.checked)}
              >
                印刷版费
              </Checkbox>
              <br />
              <Checkbox
                checked={visibleColumns.notes}
                onChange={(e) => updateVisibleColumns('notes', e.target.checked)}
              >
                備註
              </Checkbox>
            </Col>
          </Row>

          <Divider>條款設置</Divider>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="include_printing_lamination" valuePropName="checked">
                <Checkbox>價格包含印刷、防水膜</Checkbox>
              </Form.Item>
              <Form.Item name="vat_8_percent" valuePropName="checked">
                <Checkbox>報價不包括8%增值稅</Checkbox>
              </Form.Item>
              <Form.Item name="vat_10_percent" valuePropName="checked">
                <Checkbox>報價不包括10%增值稅</Checkbox>
              </Form.Item>
              <Form.Item name="minimum_order_amount" valuePropName="checked">
                <Checkbox>每單最低金額需超過3000萬越南盾</Checkbox>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="payment_method_deposit" valuePropName="checked">
                <Checkbox>付款方式: 前兩個月要訂金40%</Checkbox>
              </Form.Item>
              <Form.Item name="payment_method_monthly" valuePropName="checked">
                <Checkbox>付款方式: 月結10天</Checkbox>
              </Form.Item>
              <Form.Item name="payment_method_monthly_end" valuePropName="checked">
                <Checkbox>付款方式: 月底結算</Checkbox>
              </Form.Item>
              <Form.Item name="payment_method_30_days" valuePropName="checked">
                <Checkbox>付款方式: 30天內付款</Checkbox>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="delivery_location_warehouse" valuePropName="checked">
                <Checkbox>交貨地點: 公司倉庫</Checkbox>
              </Form.Item>
              <Form.Item name="delivery_location_pickup" valuePropName="checked">
                <Checkbox>交貨地點: 客戶到倉庫取貨</Checkbox>
              </Form.Item>
              <Form.Item
                label="自定義交貨地點"
                name="delivery_location_custom"
              >
                <Input placeholder="例如: Bến Tre" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="quote_validity" valuePropName="checked">
                <Checkbox>報價自報價之日起01個月內有效</Checkbox>
              </Form.Item>
              <Form.Item name="delivery_time_7_10" valuePropName="checked">
                <Checkbox>交貨時間: 7-10天</Checkbox>
              </Form.Item>
              <Form.Item name="delivery_time_5_7" valuePropName="checked">
                <Checkbox>交貨時間: 5-7天</Checkbox>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 報價單預覽 Modal */}
      <Modal
        title="報價單預覽"
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        width="90%"
        style={{ top: 20 }}
        footer={[
          <Button key="close" onClick={() => setPreviewVisible(false)}>
            關閉
          </Button>,
          <Button key="print" type="primary" icon={<PrinterOutlined />} onClick={handlePrint}>
            列印
          </Button>,
        ]}
      >
        {quoteData && (
          <div ref={printRef} style={{ padding: '20px', backgroundColor: 'white' }}>
            {/* 公司標題 */}
            <div style={{ textAlign: 'center', marginBottom: '20px' }}>
              <Title level={3} style={{ margin: 0 }}>
                {quoteData.config.company_name_zh}
              </Title>
              <Title level={4} style={{ margin: 0, fontWeight: 'normal' }}>
                {quoteData.config.company_name_vi}
              </Title>
              <div style={{ fontSize: '12px', marginTop: '10px' }}>
                <div>地址 Địa chỉ: {quoteData.config.address}</div>
                <div>税号 MST: {quoteData.config.tax_number}</div>
                <div>电话 Điện thoại: {quoteData.config.phone}</div>
              </div>
            </div>

            {/* 報價單標題 */}
            <div style={{ textAlign: 'center', margin: '30px 0' }}>
              <Title level={2}>BẢNG BÁO GIÁ</Title>
              <Title level={3}>報價單</Title>
            </div>

            {/* 客戶信息 */}
            <div style={{ marginBottom: '20px' }}>
              <Row>
                <Col span={12}>
                  <div>报价日期 Ngày báo giá: {quoteData.config.quote_date}</div>
                </Col>
              </Row>
              <div>Mã KH: {quoteData.config.customer_code}</div>
              <div>Khách hàng 客戶: {quoteData.config.customer_name}</div>
              <div>Mã số thuế 稅號: {quoteData.config.customer_tax_number}</div>
              <div>Địa chỉ 地址: {quoteData.config.customer_address}</div>
            </div>

            {/* 產品表格 */}
            <Table
              columns={quoteColumns}
              dataSource={quoteData.items}
              rowKey="id"
              pagination={false}
              size="small"
              bordered
              style={{
                fontSize: '11px',
                marginTop: '20px'
              }}
              components={{
                header: {
                  cell: (props: any) => (
                    <th {...props} style={{
                      ...props.style,
                      backgroundColor: '#f5f5f5',
                      fontWeight: 'bold',
                      textAlign: 'center',
                      padding: '8px 4px',
                      fontSize: '10px',
                      lineHeight: '1.2'
                    }} />
                  ),
                },
                body: {
                  cell: (props: any) => (
                    <td {...props} style={{
                      ...props.style,
                      padding: '4px',
                      fontSize: '10px',
                      lineHeight: '1.2'
                    }} />
                  ),
                },
              }}
            />

            {/* 條款 */}
            <div style={{ marginTop: '20px', fontSize: '12px' }}>
              {quoteData.config.include_printing_lamination && (
                <div>* Gía bao gồm in ấn - cán màng<br />該價格包含印刷、防水膜</div>
              )}
              {quoteData.config.vat_8_percent && (
                <div>. 报价不包括8%增值税<br />Báo giá chưa bao gồm 8% VAT.</div>
              )}
              {quoteData.config.vat_10_percent && (
                <div>. 报价不包括10%增值税<br />Báo giá chưa bao gồm 10% VAT.</div>
              )}
              {quoteData.config.minimum_order_amount && (
                <div>. 每单最低金额需超过 3000 万越南盾。<br />Mỗi đơn hàng cần có giá trị tối thiểu lớn hơn 30 triệu đồng</div>
              )}
              {quoteData.config.payment_method_deposit && (
                <div>. 付款方式: 前兩個月要訂金40%<br />Phương thức thanh toán: Cọc 40% trong 2 tháng đầu tiên.</div>
              )}
              {quoteData.config.payment_method_monthly && (
                <div>. 付款方式: 月結10天 (隔月10號前付款)<br />Phương thức thanh toán: Chốt công nợ trong tháng thanh toán trong 10 ngày kể từ ngày chốt công nợ.</div>
              )}
              {quoteData.config.payment_method_monthly_end && (
                <div>. 付款方式:<br />Phương thức thanh toán: Chốt công nợ trong tháng thanh toán vào ngày cuối tháng chốt công nợ.</div>
              )}
              {quoteData.config.payment_method_30_days && (
                <div>. 付款方式: 付款期限為自收到貨物和增值稅發票之日起 30 天內。<br />Phương thức thanh toán: Thời hạn thanh toán trong vòng 30 ngày kể từ ngày nhận hàng và hóa đơn VAT.</div>
              )}
              {quoteData.config.delivery_location_warehouse && (
                <div>. 交货地点：<br />Nơi giao hàng: Kho công ty</div>
              )}
              {quoteData.config.delivery_location_pickup && (
                <div>. 交货地点：<br />Nơi giao hàng: Khách hàng đến kho lấy hàng.</div>
              )}
              {quoteData.config.delivery_location_custom && (
                <div>. 交货地点：<br />Nơi giao hàng: {quoteData.config.delivery_location_custom}</div>
              )}
              {quoteData.config.quote_validity && (
                <div>. 报价自报价之日起 01 个月内有效。<br />Báo giá có hiệu lực trong vòng 01 tháng kể từ ngày báo.</div>
              )}
              {quoteData.config.delivery_time_7_10 && (
                <div>. 交货时间：自收到订单之日起7-10天<br />Thời gian giao hàng: 7-10 ngày kể từ ngày nhận được đơn hàng</div>
              )}
              {quoteData.config.delivery_time_5_7 && (
                <div>. 交货时间：自收到订单之日起5-7天<br />Thời gian giao hàng: 5-7 ngày kể từ ngày nhận được đơn hàng</div>
              )}
            </div>

            {/* 簽名區 */}
            <div style={{ marginTop: '40px', display: 'flex', justifyContent: 'space-between' }}>
              <div style={{ textAlign: 'center' }}>
                <div>客户确认</div>
                <div>XÁC NHẬN CỦA KHÁCH HÀNG</div>
                <div style={{ height: '80px' }}></div>
              </div>
              <div style={{ textAlign: 'center' }}>
                <div>{quoteData.config.company_name_zh}</div>
                <div>{quoteData.config.company_name_vi}</div>
                <div style={{ height: '80px' }}></div>
              </div>
            </div>
          </div>
        )}
      </Modal>
    </>
  );
};

export default QuoteGenerator;
