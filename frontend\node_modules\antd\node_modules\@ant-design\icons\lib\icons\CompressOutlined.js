"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _CompressOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/CompressOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var CompressOutlined = function CompressOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _CompressOutlined.default
  }));
};

/**![compress](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik0zMjYgNjY0SDEwNGMtOC44IDAtMTYgNy4yLTE2IDE2djQ4YzAgOC44IDcuMiAxNiAxNiAxNmgxNzR2MTc2YzAgOC44IDcuMiAxNiAxNiAxNmg0OGM4LjggMCAxNi03LjIgMTYtMTZWNjk2YzAtMTcuNy0xNC4zLTMyLTMyLTMyem0xNi01NzZoLTQ4Yy04LjggMC0xNiA3LjItMTYgMTZ2MTc2SDEwNGMtOC44IDAtMTYgNy4yLTE2IDE2djQ4YzAgOC44IDcuMiAxNiAxNiAxNmgyMjJjMTcuNyAwIDMyLTE0LjMgMzItMzJWMTA0YzAtOC44LTcuMi0xNi0xNi0xNnptNTc4IDU3Nkg2OThjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjIyNGMwIDguOCA3LjIgMTYgMTYgMTZoNDhjOC44IDAgMTYtNy4yIDE2LTE2Vjc0NGgxNzRjOC44IDAgMTYtNy4yIDE2LTE2di00OGMwLTguOC03LjItMTYtMTYtMTZ6bTAtMzg0SDc0NlYxMDRjMC04LjgtNy4yLTE2LTE2LTE2aC00OGMtOC44IDAtMTYgNy4yLTE2IDE2djIyNGMwIDE3LjcgMTQuMyAzMiAzMiAzMmgyMjJjOC44IDAgMTYtNy4yIDE2LTE2di00OGMwLTguOC03LjItMTYtMTYtMTZ6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(CompressOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CompressOutlined';
}
var _default = exports.default = RefIcon;