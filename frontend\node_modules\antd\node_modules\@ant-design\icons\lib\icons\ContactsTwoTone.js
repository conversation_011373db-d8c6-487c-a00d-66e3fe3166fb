"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _ContactsTwoTone = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/ContactsTwoTone"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var ContactsTwoTone = function ContactsTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _ContactsTwoTone.default
  }));
};

/**![contacts](data:image/svg+xml;base64,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) */
var RefIcon = /*#__PURE__*/React.forwardRef(ContactsTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ContactsTwoTone';
}
var _default = exports.default = RefIcon;