import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import CodeOutlinedSvg from "@ant-design/icons-svg/es/asn/CodeOutlined";
import AntdIcon from "../components/AntdIcon";
var CodeOutlined = function CodeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: CodeOutlinedSvg
  }));
};

/**![code](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxNiA2NzNjMCA0LjQgMy40IDggNy41IDhoMTg1YzQuMSAwIDcuNS0zLjYgNy41LTh2LTQ4YzAtNC40LTMuNC04LTcuNS04aC0xODVjLTQuMSAwLTcuNSAzLjYtNy41IDh2NDh6bS0xOTQuOSA2LjFsMTkyLTE2MWMzLjgtMy4yIDMuOC05LjEgMC0xMi4zbC0xOTItMTYwLjlBNy45NSA3Ljk1IDAgMDAzMDggMzUxdjYyLjdjMCAyLjQgMSA0LjYgMi45IDYuMUw0MjAuNyA1MTJsLTEwOS44IDkyLjJhOC4xIDguMSAwIDAwLTIuOSA2LjFWNjczYzAgNi44IDcuOSAxMC41IDEzLjEgNi4xek04ODAgMTEySDE0NGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2NzM2YzAgMTcuNyAxNC4zIDMyIDMyIDMyaDczNmMxNy43IDAgMzItMTQuMyAzMi0zMlYxNDRjMC0xNy43LTE0LjMtMzItMzItMzJ6bS00MCA3MjhIMTg0VjE4NGg2NTZ2NjU2eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(CodeOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CodeOutlined';
}
export default RefIcon;