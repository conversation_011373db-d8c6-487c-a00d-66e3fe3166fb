"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _CodeSandboxCircleFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/CodeSandboxCircleFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var CodeSandboxCircleFilled = function CodeSandboxCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _CodeSandboxCircleFilled.default
  }));
};

/**![code-sandbox-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0yNDMuNyA1ODkuMkw1MTIgNzk0IDI2OC4zIDY1My4yVjM3MS44bDExMC02My42LS40LS4yaC4yTDUxMiAyMzFsMTM0IDc3aC0uMmwtLjMuMiAxMTAuMSA2My42djI4MS40ek0zMDcuOSA1MzYuN2w4Ny42IDQ5LjlWNjgxbDk2LjcgNTUuOVY1MjQuOEwzMDcuOSA0MTguNHptMjAzLjktMTUxLjhMNDE4IDMzMWwtOTEuMSA1Mi42IDE4NS4yIDEwNyAxODUuMi0xMDYuOS05MS40LTUyLjh6bTIwIDM1Mmw5Ny4zLTU2LjJ2LTk0LjFsODctNDkuNVY0MTguNUw1MzEuOCA1MjV6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(CodeSandboxCircleFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CodeSandboxCircleFilled';
}
var _default = exports.default = RefIcon;