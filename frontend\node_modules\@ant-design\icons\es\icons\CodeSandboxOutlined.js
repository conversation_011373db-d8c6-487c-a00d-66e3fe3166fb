function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import CodeSandboxOutlinedSvg from "@ant-design/icons-svg/es/asn/CodeSandboxOutlined";
import AntdIcon from "../components/AntdIcon";
const CodeSandboxOutlined = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: CodeSandboxOutlinedSvg
}));

/**![code-sandbox](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTcwOS42IDIxMGwuNC0uMmguMkw1MTIgOTYgMzEzLjkgMjA5LjhoLS4ybC43LjNMMTUxLjUgMzA0djQxNkw1MTIgOTI4bDM2MC41LTIwOFYzMDRsLTE2Mi45LTk0ek00ODIuNyA4NDMuNkwzMzkuNiA3NjFWNjIxLjRMMjEwIDU0Ny44VjM3Mi45bDI3Mi43IDE1Ny4zdjMxMy40ek0yMzguMiAzMjEuNWwxMzQuNy03Ny44IDEzOC45IDc5LjcgMTM5LjEtNzkuOSAxMzUuMiA3OC0yNzMuOSAxNTgtMjc0LTE1OHpNODE0IDU0OC4zbC0xMjguOCA3My4xdjEzOS4xbC0xNDMuOSA4M1Y1MzAuNEw4MTQgMzczLjF2MTc1LjJ6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(CodeSandboxOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CodeSandboxOutlined';
}
export default RefIcon;